import React from 'react';
import { Button, ButtonGroup } from '@/components/ui/button';
import { AIChat } from '@/components/ai/ai-chat';
import { colors, cn, formatCurrency, formatRelativeTime, getInitials } from '@/lib/utils';
import { Moon, Sun, Palette, Zap, Building2, Users, Plane, FileText } from 'lucide-react';

function App() {
  const [darkMode, setDarkMode] = React.useState(false);
  const [selectedDemo, setSelectedDemo] = React.useState<'buttons' | 'ai-chat' | 'dashboard'>('buttons');

  React.useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  const ButtonDemo = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Core Variants</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="default">Default</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">AI-Focused Variants</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="ai" leftIcon={<Zap />}>AI Action</Button>
          <Button variant="aiSecondary">AI Secondary</Button>
          <Button variant="aiOutline">AI Outline</Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Enterprise Variants</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="enterprise" leftIcon={<Building2 />}>Enterprise</Button>
          <Button variant="success">Success</Button>
          <Button variant="warning">Warning</Button>
          <Button variant="info">Info</Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Microservice-Specific Variants</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="tender" leftIcon={<FileText />}>Tender Management</Button>
          <Button variant="drone" leftIcon={<Plane />}>Drone Services</Button>
          <Button variant="user" leftIcon={<Users />}>User Management</Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Sizes & States</h3>
        <div className="space-y-3">
          <div className="flex flex-wrap gap-3 items-center">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="xl">Extra Large</Button>
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <Button loading loadingText="Processing...">Loading</Button>
            <Button disabled>Disabled</Button>
            <Button variant="ai" loading>AI Processing</Button>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Button Groups</h3>
        <div className="space-y-3">
          <ButtonGroup>
            <Button variant="outline">First</Button>
            <Button variant="outline">Second</Button>
            <Button variant="outline">Third</Button>
          </ButtonGroup>
          
          <ButtonGroup>
            <Button variant="tender">Tenders</Button>
            <Button variant="drone">Drones</Button>
            <Button variant="user">Users</Button>
          </ButtonGroup>
        </div>
      </div>
    </div>
  );

  const AIChatDemo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Tender Management AI</h3>
          <AIChat
            context="tender"
            placeholder="Ask about tender analysis..."
            maxHeight="400px"
            showTyping={true}
            initialMessages={[
              {
                id: 'welcome-tender',
                role: 'assistant',
                content: 'Hello! I can help you with tender analysis, bid strategies, and procurement insights. What would you like to know?',
                timestamp: new Date(),
              }
            ]}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Drone Services AI</h3>
          <AIChat
            context="drone"
            placeholder="Ask about drone operations..."
            maxHeight="400px"
            showTyping={true}
            initialMessages={[
              {
                id: 'welcome-drone',
                role: 'assistant',
                content: 'Hi! I can assist with flight planning, equipment recommendations, and regulatory compliance. How can I help?',
                timestamp: new Date(),
              }
            ]}
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">General AI Assistant</h3>
        <div className="max-w-2xl">
          <AIChat
            context="general"
            placeholder="Ask me anything..."
            maxHeight="500px"
            showTyping={true}
            aiConfig={{
              provider: 'openai',
              model: 'gpt-4',
              temperature: 0.7,
              maxTokens: 1000,
            }}
            initialMessages={[
              {
                id: 'welcome-general',
                role: 'assistant',
                content: 'Welcome to the Bid Bees AI Assistant! I can help you with various tasks across our platform. What would you like to explore?',
                timestamp: new Date(),
              }
            ]}
          />
        </div>
      </div>
    </div>
  );

  const DashboardDemo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Tender Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Active Tenders</h3>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-emerald-500 rounded-full" />
              <span className="text-sm text-emerald-600 dark:text-emerald-400">24 Active</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Value</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {formatCurrency(2450000)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
              <span className="font-semibold text-emerald-600 dark:text-emerald-400">87%</span>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="tender" size="sm" fullWidth>
              View All Tenders
            </Button>
          </div>
        </div>

        {/* Drone Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Drone Fleet</h3>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-sky-500 rounded-full" />
              <span className="text-sm text-sky-600 dark:text-sky-400">12 Online</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Available</span>
              <span className="font-semibold text-green-600 dark:text-green-400">8</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">In Mission</span>
              <span className="font-semibold text-yellow-600 dark:text-yellow-400">4</span>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="drone" size="sm" fullWidth>
              Fleet Management
            </Button>
          </div>
        </div>

        {/* User Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Users</h3>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-indigo-500 rounded-full" />
              <span className="text-sm text-indigo-600 dark:text-indigo-400">156 Online</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Users</span>
              <span className="font-semibold text-gray-900 dark:text-white">1,247</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Premium</span>
              <span className="font-semibold text-purple-600 dark:text-purple-400">23</span>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="user" size="sm" fullWidth>
              Manage Users
            </Button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {[
            { action: 'New tender submitted for highway construction', time: new Date(Date.now() - 1000 * 60 * 15), type: 'tender', user: 'John Smith' },
            { action: 'Drone mission completed successfully', time: new Date(Date.now() - 1000 * 60 * 45), type: 'drone', user: 'Sarah Johnson' },
            { action: 'User registration approved', time: new Date(Date.now() - 1000 * 60 * 120), type: 'user', user: 'Mike Wilson' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center text-xs font-medium">
                  {getInitials(activity.user)}
                </div>
                <div>
                  <p className="text-sm text-gray-900 dark:text-white">{activity.action}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">by {activity.user}</p>
                </div>
              </div>
              <div className="text-right">
                <div className={cn(
                  'w-2 h-2 rounded-full mb-1 ml-auto',
                  activity.type === 'tender' && 'bg-emerald-500',
                  activity.type === 'drone' && 'bg-sky-500',
                  activity.type === 'user' && 'bg-indigo-500'
                )} />
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatRelativeTime(activity.time)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Palette className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Unified Design System
                </h1>
              </div>
              <div className="hidden sm:flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Development Mode
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <ButtonGroup>
                <Button
                  variant={selectedDemo === 'buttons' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedDemo('buttons')}
                >
                  Buttons
                </Button>
                <Button
                  variant={selectedDemo === 'ai-chat' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedDemo('ai-chat')}
                >
                  AI Chat
                </Button>
                <Button
                  variant={selectedDemo === 'dashboard' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedDemo('dashboard')}
                >
                  Dashboard
                </Button>
              </ButtonGroup>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDarkMode(!darkMode)}
              >
                {darkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {selectedDemo === 'buttons' && 'Button Components'}
            {selectedDemo === 'ai-chat' && 'AI Chat Components'}
            {selectedDemo === 'dashboard' && 'Dashboard Demo'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {selectedDemo === 'buttons' && 'Explore all button variants combining MicroSaas1 and Apptension designs'}
            {selectedDemo === 'ai-chat' && 'Interactive AI chat components for different microservice contexts'}
            {selectedDemo === 'dashboard' && 'Complete dashboard example using the unified design system'}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
          {selectedDemo === 'buttons' && <ButtonDemo />}
          {selectedDemo === 'ai-chat' && <AIChatDemo />}
          {selectedDemo === 'dashboard' && <DashboardDemo />}
        </div>
      </main>
    </div>
  );
}

export default App;
