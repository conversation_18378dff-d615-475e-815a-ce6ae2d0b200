#!/bin/bash

cd bid_bees_full_project

# BidBees Platform Stop Script
# ============================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}🛑 $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }

print_info "Stopping BidBees Platform..."

# Stop main application
if [ -f .main-app.pid ]; then
    PID=$(cat .main-app.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        print_success "Main app stopped (PID: $PID)"
    fi
    rm -f .main-app.pid
fi

# Stop any processes on our ports
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
lsof -ti:5000 | xargs kill -9 2>/dev/null || true

# Stop microservices
cd microservices
docker-compose down
cd ..

print_success "All BidBees services stopped!"

