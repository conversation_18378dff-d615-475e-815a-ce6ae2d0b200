import dns from 'dns';
import { join } from 'path';

import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import legacy from '@vitejs/plugin-legacy';
import react from '@vitejs/plugin-react';
import { UserConfig, defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';
import viteTsConfigPaths from 'vite-tsconfig-paths';

dns.setDefaultResultOrder('verbatim');

export default defineConfig(({ mode }): UserConfig => {
  const env = loadEnv(mode, process.cwd());

  // Suppress CommonJS deprecation warning
  process.env.VITE_CJS_IGNORE_WARNING = 'true';

  // expose .env as process.env instead of import.meta since jest does not import meta yet
  const envWithProcessPrefix = Object.entries(env).reduce((prev, [key, val]) => {
    return {
      ...prev,
      ['process.env.' + key]: `"${val}"`,
    };
  }, {});

  return {
    cacheDir: '../../node_modules/.vite/webapp',

    server: {
      port: 3000,
      host: 'localhost',
      open: true,
      proxy: {
        '/api': {
          target: 'http://localhost:5001',
          changeOrigin: true,
          ws: false, // Disable WebSocket proxying
        },
        '/static/graphene_django': {
          target: 'http://localhost:5001',
          changeOrigin: true,
        },
      },
    },

    preview: {
      port: 4300,
      host: 'localhost',
    },

    define: envWithProcessPrefix,

    css: {
      postcss: {
        plugins: [require('tailwindcss')({ config: join(__dirname, 'tailwind.config.ts') }), require('autoprefixer')()],
      },
    },

    plugins: [
      legacy(),
      react(),
      viteTsConfigPaths({
        projects: ['../../tsconfig.base.json'],
      }),
      svgr({
        svgrOptions: { icon: true },
        include: ['**/*.svg', '**/*.svg?react'],
        exclude: [],
      }),
      viteCommonjs(),
    ],

    build: {
      outDir: 'build',
    },

    optimizeDeps: {
      // Pre-bundle dependencies to avoid CommonJS warnings
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@apollo/client',
        'graphql',
        // Add other frequently used dependencies here
      ],
    },

    resolve: {
      alias: {
        fs: require.resolve('rollup-plugin-node-builtins'),
        path: require.resolve('rollup-plugin-node-builtins'),
      },
    },
  };
});
