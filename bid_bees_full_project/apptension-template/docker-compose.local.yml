version: '3.4'

volumes:
  web_backend_db_data:
    name: '${PROJECT_NAME}-web-backend-db-data'
    external: true

  web_backend_staticfiles: {}

  redis_cache:
    driver: local

services:
  db:
    volumes:
      - web_backend_db_data:/var/lib/postgresql/data

  backend:
    volumes:
      - ./packages/webapp-libs/webapp-emails/build/email-renderer/:/app/scripts/runtime/email/renderer:ro
      - ./packages/backend/:/app
      - ./packages/backend/docs:/app/docs
      - /app/__pypackages__
      - /app/node_modules
      - /app/cdk.out
      - web_backend_staticfiles:/app/static
    env_file:
      - ./packages/backend/.env
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - WORKERS_EVENT_BUS_NAME=${PROJECT_NAME}-${ENV_STAGE}-workers
    depends_on:
      - db
      - localstack
      - mailcatcher
      # - workers
      - flower

  celery_beat:
    env_file:
      - ./packages/backend/.env

  celery_default:
    command: ["./scripts/runtime/run_local_celery_worker_default.sh"]
    volumes:
      - ./packages/backend/:/app
      - /app/__pypackages__
    env_file:
      - ./packages/backend/.env
    environment:
      - AWS_ACCESS_KEY_ID=foo
      - AWS_SECRET_ACCESS_KEY=bar
      - AWS_DEFAULT_REGION=eu-west-1

# workers:
#   volumes:
#     - ./packages/workers/:/app/packages/workers/
#     - /app/packages/workers/node_modules/
#     - /app/packages/workers/__pypackages__/
#   env_file:
#     - .env
#     - ./packages/workers/.env
#   environment:
#     - AWS_ENDPOINT_URL=http://localstack:4566
#     - ENV_STAGE=${ENV_STAGE:-}
#   depends_on:
#     - db
#     - mailcatcher
#   ports:
#     - '3005:3005'

  redis:
    volumes:
      - redis_cache:/data

  flower:
    image: '${PROJECT_NAME}/backend'
    command: ['./scripts/runtime/run_local_celery_flower.sh']
    env_file:
      - ./packages/backend/.env
    ports:
      - '5555:5555'
    depends_on:
      redis:
        condition: service_healthy

  localstack:
    image: localstack/localstack:2.3.0
    ports:
      - '4566:4566'
    environment:
      - SERVICES=serverless,events,cloudformation,ses,secretsmanager
      - DEFAULT_REGION=eu-west-1
      - DEBUG=1
      - LAMBDA_EXECUTOR=docker
      - LAMBDA_REMOTE_DOCKER=true
      - DOCKER_HOST=unix:///var/run/docker.sock
      - AWS_ACCESS_KEY_ID=foo
      - AWS_SECRET_ACCESS_KEY=bar
      - HOST_TMP_FOLDER=/tmp
    volumes:
      - '/tmp/localstack:/tmp/localstack'
      - '/var/run/docker.sock:/var/run/docker.sock'
    privileged: true
    depends_on:
      - db
    links:
      - db

  mailcatcher:
    image: sj26/mailcatcher:v0.9.0
    ports:
      - '1080:1080'
      - '1025:1025'
    restart: always
