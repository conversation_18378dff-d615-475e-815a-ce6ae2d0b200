import * as React from 'react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';

/**
 * AI Chat Component
 * Combines MicroSaas1's AI capabilities with enterprise-grade UI
 * Designed for integration across bid_bees microservices
 */

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    workflowId?: string;
    tokens?: number;
    cost?: number;
    model?: string;
  };
}

export interface AIChatProps {
  /** Unique identifier for the chat session */
  sessionId?: string;
  /** AI workflow ID for n8n integration */
  workflowId?: string;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Callback when a response is received */
  onResponse?: (response: Message) => void;
  /** Callback when a message is sent */
  onMessageSent?: (message: Message) => void;
  /** Enable Supabase integration for persistence */
  supabaseIntegration?: boolean;
  /** Theme variant */
  theme?: 'light' | 'dark' | 'auto';
  /** Maximum height of the chat container */
  maxHeight?: string;
  /** Initial messages */
  initialMessages?: Message[];
  /** Disable the chat input */
  disabled?: boolean;
  /** Show typing indicator */
  showTyping?: boolean;
  /** Custom system prompt */
  systemPrompt?: string;
  /** AI provider configuration */
  aiConfig?: {
    provider: 'openai' | 'anthropic' | 'custom';
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  /** Microservice context for specialized AI responses */
  context?: 'tender' | 'drone' | 'user' | 'general';
}

const AIChat = React.forwardRef<HTMLDivElement, AIChatProps>(
  ({
    sessionId = 'default',
    workflowId,
    placeholder = "Ask AI anything...",
    onResponse,
    onMessageSent,
    supabaseIntegration = true,
    theme = 'auto',
    maxHeight = '400px',
    initialMessages = [],
    disabled = false,
    showTyping = false,
    systemPrompt,
    aiConfig = {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 1000,
    },
    context = 'general',
    className,
    ...props
  }, ref) => {
    const [messages, setMessages] = React.useState<Message[]>(initialMessages);
    const [input, setInput] = React.useState('');
    const [isLoading, setIsLoading] = React.useState(false);
    const messagesEndRef = React.useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom when new messages arrive
    React.useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    // Generate context-specific system prompts
    const getSystemPrompt = React.useCallback(() => {
      if (systemPrompt) return systemPrompt;

      const contextPrompts = {
        tender: "You are an AI assistant specialized in tender management and procurement processes. Help users with bid analysis, tender requirements, and submission strategies.",
        drone: "You are an AI assistant for drone contractor services. Assist with flight planning, regulatory compliance, equipment recommendations, and project management.",
        user: "You are an AI assistant for user management and authentication. Help with account setup, permissions, security best practices, and user experience optimization.",
        general: "You are a helpful AI assistant for the bid_bees platform. Provide accurate, helpful responses while maintaining a professional tone."
      };

      return contextPrompts[context];
    }, [context, systemPrompt]);

    // Handle message submission
    const handleSubmit = React.useCallback(async (e: React.FormEvent) => {
      e.preventDefault();
      if (!input.trim() || isLoading || disabled) return;

      const userMessage: Message = {
        id: `user-${Date.now()}`,
        role: 'user',
        content: input.trim(),
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setInput('');
      setIsLoading(true);

      // Call onMessageSent callback
      onMessageSent?.(userMessage);

      try {
        // Simulate AI response (replace with actual AI service call)
        await new Promise(resolve => setTimeout(resolve, 1000));

        const aiResponse: Message = {
          id: `ai-${Date.now()}`,
          role: 'assistant',
          content: `This is a simulated AI response to: "${userMessage.content}". In a real implementation, this would connect to ${aiConfig.provider} using the ${aiConfig.model} model.`,
          timestamp: new Date(),
          metadata: {
            workflowId,
            tokens: 150,
            cost: 0.003,
            model: aiConfig.model,
          },
        };

        setMessages(prev => [...prev, aiResponse]);
        onResponse?.(aiResponse);

        // TODO: Integrate with Supabase for persistence
        if (supabaseIntegration) {
          console.log('Saving to Supabase:', { userMessage, aiResponse });
        }

      } catch (error) {
        console.error('AI Chat Error:', error);
        const errorMessage: Message = {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, errorMessage]);
      } finally {
        setIsLoading(false);
      }
    }, [input, isLoading, disabled, onMessageSent, onResponse, workflowId, aiConfig, supabaseIntegration]);

    // Message component
    const MessageBubble = ({ message }: { message: Message }) => (
      <div
        className={cn(
          'flex w-full mb-4',
          message.role === 'user' ? 'justify-end' : 'justify-start'
        )}
      >
        <div
          className={cn(
            'max-w-[80%] px-4 py-2 rounded-lg',
            message.role === 'user'
              ? 'bg-blue-600 text-white rounded-br-sm'
              : 'bg-gray-100 text-gray-900 rounded-bl-sm dark:bg-gray-800 dark:text-gray-100',
            message.role === 'assistant' && context === 'tender' && 'bg-emerald-50 border border-emerald-200 dark:bg-emerald-950 dark:border-emerald-800',
            message.role === 'assistant' && context === 'drone' && 'bg-sky-50 border border-sky-200 dark:bg-sky-950 dark:border-sky-800',
            message.role === 'assistant' && context === 'user' && 'bg-indigo-50 border border-indigo-200 dark:bg-indigo-950 dark:border-indigo-800'
          )}
        >
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          {message.metadata && (
            <div className="mt-2 text-xs opacity-70">
              {message.metadata.model && <span>Model: {message.metadata.model}</span>}
              {message.metadata.tokens && <span> • Tokens: {message.metadata.tokens}</span>}
              {message.metadata.cost && <span> • Cost: ${message.metadata.cost.toFixed(4)}</span>}
            </div>
          )}
        </div>
      </div>
    );

    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col border rounded-lg bg-white dark:bg-gray-900',
          'shadow-sm hover:shadow-md transition-shadow',
          className
        )}
        style={{ maxHeight }}
        {...props}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800 rounded-t-lg">
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full',
              isLoading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'
            )} />
            <span className="text-sm font-medium">
              AI Assistant {context !== 'general' && `(${context})`}
            </span>
          </div>
          {workflowId && (
            <span className="text-xs text-gray-500">
              Workflow: {workflowId}
            </span>
          )}
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-2" style={{ maxHeight: `calc(${maxHeight} - 120px)` }}>
          {messages.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              <p className="text-sm">Start a conversation with AI</p>
              <p className="text-xs mt-1">
                {getSystemPrompt()}
              </p>
            </div>
          )}
          
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {showTyping && isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-lg rounded-bl-sm">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <form onSubmit={handleSubmit} className="p-4 border-t bg-gray-50 dark:bg-gray-800 rounded-b-lg">
          <div className="flex gap-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={placeholder}
              disabled={disabled || isLoading}
              className={cn(
                'flex-1 px-3 py-2 text-sm border rounded-md',
                'bg-white dark:bg-gray-900',
                'border-gray-300 dark:border-gray-600',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
            />
            <Button
              type="submit"
              variant="ai"
              size="sm"
              disabled={!input.trim() || isLoading || disabled}
              loading={isLoading}
            >
              Send
            </Button>
          </div>
        </form>
      </div>
    );
  }
);

AIChat.displayName = 'AIChat';

export { AIChat };
export type { AIChatProps, Message };
