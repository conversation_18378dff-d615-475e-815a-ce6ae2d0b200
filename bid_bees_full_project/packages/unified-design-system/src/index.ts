/**
 * Unified Design System for bid_bees microservices
 * Combining MicroSaas1's AI-focused design with Apptension's enterprise architecture
 */

// Core utilities
export { cn, formatCurrency, formatDate, formatRelativeTime, truncateText, getInitials, isValidEmail, generateId, debounce, deepMerge, formatBytes, sleep, isBrowser, safeJsonParse, slugify, titleCase } from './lib/utils';

// Design tokens
export { colors, getColorValue, withOpacity, generateCSSVariables } from './tokens/colors';
export type { ColorScale, SemanticColor, StatusColor } from './tokens/colors';

// UI Components
export { Button, ButtonGroup, buttonVariants } from './components/ui/button';
export type { ButtonProps, ButtonGroupProps } from './components/ui/button';

// AI Components
export { AIChat } from './components/ai/ai-chat';
export type { AIChatProps, Message } from './components/ai/ai-chat';

// Re-export commonly used external dependencies for consistency
export { cva, type VariantProps } from 'class-variance-authority';
export { Slot } from '@radix-ui/react-slot';

// Version
export const VERSION = '1.0.0';

// Theme configuration
export const THEME_CONFIG = {
  defaultTheme: 'light' as const,
  themes: ['light', 'dark'] as const,
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  animations: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
  },
} as const;

export type Theme = typeof THEME_CONFIG.themes[number];
export type Breakpoint = keyof typeof THEME_CONFIG.breakpoints;
