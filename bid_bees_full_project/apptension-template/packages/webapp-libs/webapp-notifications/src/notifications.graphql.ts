import { gql } from '@sb/webapp-api-client/graphql';

export const notificationsListQuery = gql(/* GraphQL */ `
  query notificationsListQuery($count: Int = 20, $cursor: String) {
    hasUnreadNotifications
    allNotifications(first: $count, after: $cursor) {
      edges {
        node {
          id
          data
          createdAt
          readAt
          type
          issuer {
            id
            avatar
            email
          }
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
  }
`);

export const notificationCreatedSubscription = gql(/* GraphQL */ `
  subscription NotificationCreatedSubscription {
    notificationCreated {
      notification {
        id
        data
        createdAt
        readAt
        type
        issuer {
          id
          avatar
          email
        }
      }
    }
  }
`);
