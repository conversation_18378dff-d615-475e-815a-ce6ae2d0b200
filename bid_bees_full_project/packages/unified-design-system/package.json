{"name": "@bid-bees/unified-design-system", "version": "1.0.0", "description": "Unified design system combining MicroSaas1's AI-focused UI with Apptension's enterprise architecture", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "keywords": ["design-system", "react", "typescript", "tailwindcss", "radix-ui", "ai-components", "enterprise", "microservices"], "author": "Bid Bees Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bid-bees/unified-design-system"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./components": {"import": "./dist/components/index.esm.js", "require": "./dist/components/index.js", "types": "./dist/components/index.d.ts"}, "./tokens": {"import": "./dist/tokens/index.esm.js", "require": "./dist/tokens/index.js", "types": "./dist/tokens/index.d.ts"}, "./utils": {"import": "./dist/lib/utils.esm.js", "require": "./dist/lib/utils.js", "types": "./dist/lib/utils.d.ts"}}}