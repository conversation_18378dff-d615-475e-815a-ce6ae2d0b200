/**
 * Unified Color System for bid_bees microservices
 * Combines MicroSaas1's AI-focused palette with Apptension's enterprise colors
 */

export const colors = {
  // Primary brand colors (AI-focused from MicroSaas1)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },

  // AI-specific colors (from MicroSaas1)
  ai: {
    primary: '#8b5cf6',
    secondary: '#a78bfa',
    accent: '#c084fc',
    gradient: {
      from: '#667eea',
      to: '#764ba2',
      css: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    success: '#10b981',
    processing: '#f59e0b',
    error: '#ef4444',
  },

  // Enterprise colors (from Apptension)
  enterprise: {
    neutral: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981',
      600: '#059669',
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
    },
  },

  // Semantic colors for consistent usage
  semantic: {
    background: {
      primary: '#ffffff',
      secondary: '#f8fafc',
      tertiary: '#f1f5f9',
      inverse: '#0f172a',
    },
    foreground: {
      primary: '#0f172a',
      secondary: '#475569',
      tertiary: '#64748b',
      inverse: '#ffffff',
      muted: '#94a3b8',
    },
    border: {
      default: '#e2e8f0',
      muted: '#f1f5f9',
      strong: '#cbd5e1',
    },
    accent: {
      default: '#f1f5f9',
      hover: '#e2e8f0',
      active: '#cbd5e1',
    },
  },

  // Dark mode variants
  dark: {
    background: {
      primary: '#0f172a',
      secondary: '#1e293b',
      tertiary: '#334155',
      inverse: '#ffffff',
    },
    foreground: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
      tertiary: '#94a3b8',
      inverse: '#0f172a',
      muted: '#64748b',
    },
    border: {
      default: '#334155',
      muted: '#1e293b',
      strong: '#475569',
    },
    accent: {
      default: '#1e293b',
      hover: '#334155',
      active: '#475569',
    },
  },

  // Status colors for microservices
  status: {
    tender: {
      active: '#10b981',
      pending: '#f59e0b',
      closed: '#64748b',
      awarded: '#8b5cf6',
    },
    user: {
      online: '#10b981',
      away: '#f59e0b',
      offline: '#64748b',
      premium: '#8b5cf6',
    },
    drone: {
      available: '#10b981',
      busy: '#f59e0b',
      maintenance: '#ef4444',
      offline: '#64748b',
    },
    workflow: {
      running: '#3b82f6',
      success: '#10b981',
      failed: '#ef4444',
      paused: '#f59e0b',
    },
  },

  // Component-specific colors
  components: {
    button: {
      primary: {
        bg: '#3b82f6',
        hover: '#2563eb',
        active: '#1d4ed8',
        text: '#ffffff',
      },
      secondary: {
        bg: '#f1f5f9',
        hover: '#e2e8f0',
        active: '#cbd5e1',
        text: '#334155',
      },
      ai: {
        bg: 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
        hover: 'linear-gradient(135deg, #7c3aed 0%, #2563eb 100%)',
        text: '#ffffff',
      },
    },
    input: {
      bg: '#ffffff',
      border: '#e2e8f0',
      borderFocus: '#3b82f6',
      text: '#0f172a',
      placeholder: '#94a3b8',
    },
    card: {
      bg: '#ffffff',
      border: '#e2e8f0',
      shadow: 'rgba(15, 23, 42, 0.08)',
    },
  },
} as const;

// Type definitions for better TypeScript support
export type ColorScale = typeof colors.primary;
export type SemanticColor = keyof typeof colors.semantic;
export type StatusColor = keyof typeof colors.status;

// Helper functions for color manipulation
export function getColorValue(colorPath: string): string {
  const keys = colorPath.split('.');
  let value: any = colors;
  
  for (const key of keys) {
    value = value[key];
    if (value === undefined) {
      console.warn(`Color path "${colorPath}" not found`);
      return '#000000';
    }
  }
  
  return typeof value === 'string' ? value : '#000000';
}

export function withOpacity(color: string, opacity: number): string {
  // Convert hex to rgba with opacity
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

// CSS custom properties for runtime theme switching
export function generateCSSVariables(theme: 'light' | 'dark' = 'light') {
  const themeColors = theme === 'dark' ? colors.dark : colors.semantic;
  
  return Object.entries(themeColors).reduce((acc, [key, value]) => {
    if (typeof value === 'object') {
      Object.entries(value).forEach(([subKey, subValue]) => {
        acc[`--color-${key}-${subKey}`] = subValue;
      });
    } else {
      acc[`--color-${key}`] = value;
    }
    return acc;
  }, {} as Record<string, string>);
}
