const http = require('http');
const url = require('url');

// Mock data matching the exact GraphQL schema
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'Demo',
  lastName: 'User',
  roles: ['user'],
  avatar: null,
  otpVerified: false,
  otpEnabled: false,
  tenants: [
    {
      id: '1',
      name: 'Demo Personal',
      type: 'default',
      membership: {
        id: '1',
        role: 'OWNER',
        invitationAccepted: true,
        inviteeEmailAddress: null,
        invitationToken: null,
        userId: '1',
        firstName: 'Demo',
        lastName: 'User',
        userEmail: '<EMAIL>',
        avatar: null,
        __typename: 'TenantMembershipType'
      },
      __typename: 'TenantType'
    },
    {
      id: '2',
      name: 'Demo Organization',
      type: 'organization',
      membership: {
        id: '2',
        role: 'ADMIN',
        invitationAccepted: true,
        inviteeEmailAddress: null,
        invitationToken: null,
        userId: '1',
        firstName: 'Demo',
        lastName: 'User',
        userEmail: '<EMAIL>',
        avatar: null,
        __typename: 'TenantMembershipType'
      },
      __typename: 'TenantType'
    }
  ],
  __typename: 'CurrentUserType'
};

const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);

  if (parsedUrl.pathname.includes('/api/graphql')) {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      res.setHeader('Content-Type', 'application/json');

      // Parse the GraphQL query
      let query = '';
      let operationName = '';
      try {
        if (body) {
          const parsed = JSON.parse(body);
          query = parsed.query || '';
          operationName = parsed.operationName || '';
        }
      } catch (e) {
        query = '';
      }



      // Handle different GraphQL operations
      if (query.includes('tokenAuth') || query.includes('loginFormMutation')) {
        // Mock successful login
        res.end(JSON.stringify({
          data: {
            tokenAuth: {
              access: 'mock-access-token',
              refresh: 'mock-refresh-token',
              otpAuthToken: null,
              __typename: 'ObtainTokenMutationPayload'
            }
          }
        }));
      } else if (operationName === 'commonQueryCurrentUserQuery' || query.includes('commonQueryCurrentUserQuery') || query.includes('currentUser')) {
        // Mock the main common query that loads user and tenants

        res.end(JSON.stringify({
          data: {
            currentUser: mockUser,
            __typename: 'Query'
          }
        }));
      } else if (query.includes('allTenants')) {
        // Mock tenants query
        res.end(JSON.stringify({
          data: {
            allTenants: {
              edges: mockUser.tenants.map(tenant => ({
                node: tenant,
                __typename: 'TenantTypeEdge'
              })),
              pageInfo: {
                hasNextPage: false,
                endCursor: null,
                __typename: 'PageInfo'
              },
              __typename: 'TenantConnection'
            },
            __typename: 'Query'
          }
        }));
      } else if (query.includes('notificationsListQuery') || query.includes('allNotifications')) {
        // Mock notifications query
        res.end(JSON.stringify({
          data: {
            hasUnreadNotifications: false,
            allNotifications: {
              edges: [],
              pageInfo: {
                hasNextPage: false,
                endCursor: null,
                __typename: 'PageInfo'
              },
              __typename: 'NotificationConnection'
            },
            __typename: 'Query'
          }
        }));
      } else {
        // Default response with current user
        res.end(JSON.stringify({
          data: {
            currentUser: mockUser,
            hasUnreadNotifications: false,
            __typename: 'Query'
          }
        }));
      }
    });
  } else if (parsedUrl.pathname.includes('/api/auth')) {
    // Handle auth endpoints
    res.setHeader('Content-Type', 'application/json');
    
    if (parsedUrl.pathname.includes('/token-refresh')) {
      res.end(JSON.stringify({ success: true }));
    } else if (parsedUrl.pathname.includes('/logout')) {
      res.end(JSON.stringify({ success: true }));
    } else if (parsedUrl.pathname.includes('/me')) {
      res.end(JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'User',
        avatar: null
      }));
    } else {
      res.end(JSON.stringify({ success: true }));
    }
  } else {
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({ status: 'ok' }));
  }
});

const PORT = 5001;
server.listen(PORT, () => {
  console.log(`Mock API server running on http://localhost:${PORT}`);
});