# Unified Design System

A comprehensive design system that combines MicroSaas1's modern AI-focused UI components with Apptension's enterprise-grade architecture, specifically designed for the bid_bees microservices ecosystem.

## 🎯 Overview

This design system provides:
- ✅ **Modern AI-focused components** from MicroSaas1
- ✅ **Enterprise-grade architecture** from Apptension
- ✅ **Unified design tokens** for consistent theming
- ✅ **TypeScript support** with full type safety
- ✅ **Dark/light mode** support
- ✅ **Microservice-specific variants** for different domains

## 🚀 Installation

```bash
npm install @bid-bees/unified-design-system
# or
yarn add @bid-bees/unified-design-system
# or
pnpm add @bid-bees/unified-design-system
```

## 📦 Dependencies

This package requires the following peer dependencies:

```bash
npm install react react-dom tailwindcss
```

## 🎨 Usage

### Basic Button Component

```tsx
import { Button } from '@bid-bees/unified-design-system';

function MyComponent() {
  return (
    <div className="space-y-4">
      {/* Standard button */}
      <Button variant="default">Click me</Button>
      
      {/* AI-focused button */}
      <Button variant="ai">AI Action</Button>
      
      {/* Enterprise button */}
      <Button variant="enterprise">Enterprise Action</Button>
      
      {/* Loading state */}
      <Button loading loadingText="Processing...">
        Submit
      </Button>
      
      {/* With icons */}
      <Button 
        leftIcon={<PlusIcon />}
        rightIcon={<ArrowRightIcon />}
      >
        Add Item
      </Button>
    </div>
  );
}
```

### Using Design Tokens

```tsx
import { colors, cn } from '@bid-bees/unified-design-system';

function CustomComponent() {
  return (
    <div 
      className={cn(
        'p-4 rounded-lg',
        'bg-white border border-gray-200',
        'hover:shadow-lg transition-shadow'
      )}
      style={{
        backgroundColor: colors.ai.primary,
        color: colors.semantic.foreground.inverse
      }}
    >
      Custom styled component
    </div>
  );
}
```

### Utility Functions

```tsx
import { 
  formatCurrency, 
  formatDate, 
  formatRelativeTime,
  truncateText,
  getInitials 
} from '@bid-bees/unified-design-system';

function DataDisplay({ user, amount, date, description }) {
  return (
    <div>
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
          {getInitials(user.name)}
        </div>
        <span>{user.name}</span>
      </div>
      
      <p className="text-lg font-semibold">
        {formatCurrency(amount)}
      </p>
      
      <p className="text-sm text-gray-600">
        {formatRelativeTime(date)} • {formatDate(date)}
      </p>
      
      <p className="text-sm">
        {truncateText(description, 100)}
      </p>
    </div>
  );
}
```

## 🎨 Component Variants

### Button Variants

- `default` - Standard primary button
- `destructive` - For dangerous actions
- `outline` - Outlined button
- `secondary` - Secondary actions
- `ghost` - Minimal button
- `link` - Link-styled button
- `ai` - AI-focused gradient button
- `aiSecondary` - Secondary AI button
- `aiOutline` - Outlined AI button
- `enterprise` - Enterprise-grade button
- `success` - Success actions
- `warning` - Warning actions
- `info` - Informational actions
- `tender` - Tender management specific
- `drone` - Drone service specific
- `user` - User management specific

### Button Sizes

- `default` - Standard size (h-10)
- `sm` - Small size (h-9)
- `lg` - Large size (h-11)
- `xl` - Extra large size (h-12)
- `icon` - Square icon button (h-10 w-10)
- `iconSm` - Small icon button (h-8 w-8)
- `iconLg` - Large icon button (h-12 w-12)

## 🌈 Color System

The design system includes a comprehensive color palette:

### Primary Colors
- AI-focused gradients and accents
- Enterprise neutral tones
- Semantic colors for consistent usage

### Status Colors
- Tender: active, pending, closed, awarded
- User: online, away, offline, premium
- Drone: available, busy, maintenance, offline
- Workflow: running, success, failed, paused

### Theme Support
- Light mode (default)
- Dark mode
- Automatic theme switching
- CSS custom properties for runtime changes

## 🛠 Development

### Building the Package

```bash
npm run build
```

### Running Tests

```bash
npm test
npm run test:watch
npm run test:coverage
```

### Linting

```bash
npm run lint
npm run lint:fix
```

### Type Checking

```bash
npm run type-check
```

## 📚 Storybook

View all components in Storybook:

```bash
npm run storybook
```

## 🏗 Architecture

This design system follows these principles:

1. **Composition over Configuration** - Components are built to be composed together
2. **TypeScript First** - Full type safety and IntelliSense support
3. **Accessibility** - Built on Radix UI primitives for accessibility
4. **Performance** - Optimized bundle size and runtime performance
5. **Consistency** - Unified design tokens across all components
6. **Flexibility** - Easy to customize and extend

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add tests for new components
3. Update documentation and Storybook stories
4. Ensure TypeScript types are properly defined
5. Test across different microservices

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Related Packages

- `@bid-bees/ai-services` - AI integration services
- `@bid-bees/microservice-adapters` - Microservice UI adapters
- `@bid-bees/supabase-extensions` - Supabase integration utilities
