/**
 * Integration Demo: Unified Design System with Apptension Template
 * 
 * This file demonstrates how to integrate the unified design system
 * into the Apptension template, combining MicroSaas1's AI components
 * with enterprise-grade architecture.
 */

import React from 'react';
import { 
  Button, 
  ButtonGroup, 
  AIChat, 
  colors, 
  cn,
  formatCurrency,
  formatRelativeTime,
  type Message 
} from '@bid-bees/unified-design-system';

// Example: Enhanced Dashboard Component
export const EnhancedDashboard: React.FC = () => {
  const [messages, setMessages] = React.useState<Message[]>([]);
  const [selectedService, setSelectedService] = React.useState<'tender' | 'drone' | 'user'>('tender');

  const handleAIResponse = (response: Message) => {
    console.log('AI Response received:', response);
    setMessages(prev => [...prev, response]);
  };

  const handleMessageSent = (message: Message) => {
    console.log('Message sent:', message);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with unified design system */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Bid Bees Dashboard
              </h1>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  AI Services Active
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Service selector with unified buttons */}
              <ButtonGroup>
                <Button
                  variant={selectedService === 'tender' ? 'tender' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedService('tender')}
                >
                  Tenders
                </Button>
                <Button
                  variant={selectedService === 'drone' ? 'drone' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedService('drone')}
                >
                  Drones
                </Button>
                <Button
                  variant={selectedService === 'user' ? 'user' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedService('user')}
                >
                  Users
                </Button>
              </ButtonGroup>
              
              <Button variant="ai" size="sm">
                AI Insights
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Service-specific dashboard cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Tender Management Card */}
              <div className={cn(
                'bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6',
                selectedService === 'tender' && 'ring-2 ring-emerald-500 ring-opacity-50'
              )}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Active Tenders
                  </h3>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full" />
                    <span className="text-sm text-emerald-600 dark:text-emerald-400">
                      24 Active
                    </span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Total Value</span>
                    <span className="font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(2450000)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Response Time</span>
                    <span className="font-semibold text-gray-900 dark:text-white">
                      2.3 hours
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
                    <span className="font-semibold text-emerald-600 dark:text-emerald-400">
                      87%
                    </span>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="tender" size="sm" fullWidth>
                    View All Tenders
                  </Button>
                </div>
              </div>

              {/* Drone Services Card */}
              <div className={cn(
                'bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6',
                selectedService === 'drone' && 'ring-2 ring-sky-500 ring-opacity-50'
              )}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Drone Fleet
                  </h3>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-sky-500 rounded-full" />
                    <span className="text-sm text-sky-600 dark:text-sky-400">
                      12 Online
                    </span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Available</span>
                    <span className="font-semibold text-green-600 dark:text-green-400">8</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">In Mission</span>
                    <span className="font-semibold text-yellow-600 dark:text-yellow-400">4</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Maintenance</span>
                    <span className="font-semibold text-red-600 dark:text-red-400">2</span>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="drone" size="sm" fullWidth>
                    Fleet Management
                  </Button>
                </div>
              </div>

              {/* User Management Card */}
              <div className={cn(
                'bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6 md:col-span-2',
                selectedService === 'user' && 'ring-2 ring-indigo-500 ring-opacity-50'
              )}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    User Activity
                  </h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        156 Online
                      </span>
                    </div>
                    <Button variant="user" size="sm">
                      Manage Users
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">1,247</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">156</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Active Now</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">89</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">New Today</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">23</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Premium</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Recent Activity
              </h3>
              <div className="space-y-3">
                {[
                  { action: 'New tender submitted', time: new Date(Date.now() - 1000 * 60 * 15), type: 'tender' },
                  { action: 'Drone mission completed', time: new Date(Date.now() - 1000 * 60 * 45), type: 'drone' },
                  { action: 'User registration approved', time: new Date(Date.now() - 1000 * 60 * 120), type: 'user' },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        'w-2 h-2 rounded-full',
                        activity.type === 'tender' && 'bg-emerald-500',
                        activity.type === 'drone' && 'bg-sky-500',
                        activity.type === 'user' && 'bg-indigo-500'
                      )} />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {activity.action}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatRelativeTime(activity.time)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* AI Chat Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <AIChat
                sessionId="dashboard-chat"
                workflowId="dashboard-assistant"
                context={selectedService}
                placeholder={`Ask about ${selectedService} management...`}
                onResponse={handleAIResponse}
                onMessageSent={handleMessageSent}
                supabaseIntegration={true}
                maxHeight="600px"
                showTyping={true}
                aiConfig={{
                  provider: 'openai',
                  model: 'gpt-4',
                  temperature: 0.7,
                  maxTokens: 1000,
                }}
                initialMessages={[
                  {
                    id: 'welcome',
                    role: 'assistant',
                    content: `Welcome to the ${selectedService} management assistant! I can help you with analysis, insights, and recommendations.`,
                    timestamp: new Date(),
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Example: Integration with existing Apptension components
export const IntegratedFormExample: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
  };

  return (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
        Enhanced Form with Unified Design
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Project Name
          </label>
          <input
            type="text"
            className={cn(
              'w-full px-3 py-2 border border-gray-300 dark:border-gray-600',
              'rounded-md shadow-sm bg-white dark:bg-gray-900',
              'text-gray-900 dark:text-white',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            )}
            placeholder="Enter project name"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Service Type
          </label>
          <select className={cn(
            'w-full px-3 py-2 border border-gray-300 dark:border-gray-600',
            'rounded-md shadow-sm bg-white dark:bg-gray-900',
            'text-gray-900 dark:text-white',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          )}>
            <option value="">Select service type</option>
            <option value="tender">Tender Management</option>
            <option value="drone">Drone Services</option>
            <option value="user">User Management</option>
          </select>
        </div>
        
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            fullWidth
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="ai"
            fullWidth
            loading={isSubmitting}
            loadingText="Creating..."
          >
            Create Project
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedDashboard;
