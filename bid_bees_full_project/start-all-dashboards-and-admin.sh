#!/bin/bash

cd bid_bees_full_project

# 🚀 BidBees Complete Dashboard & Admin Interface Startup Script
# This script starts ALL dashboards, admin interfaces, and monitoring tools

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 1
    else
        return 0
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $name to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_success "$name is ready!"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    print_warning "$name may not be fully ready yet"
    return 1
}

# Kill any existing processes on our ports
cleanup_ports() {
    print_status "Cleaning up existing processes..."
    
    ports=(3000 3001 3002 3003 3004 3005 3006 3007 3008 3009 3010 8000 8001 8002 8080 5000 5001 5432)
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
            print_warning "Killing process on port $port"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
        fi
    done
    
    sleep 2
}

# Start main applications
start_main_apps() {
    print_status "Starting main applications..."
    
    # 1. Main Client App (Port 3000)
    print_success "🏠 Starting Main Client App on http://localhost:3000"
    PORT=3000 npm run dev:legacy > logs/main-app.log 2>&1 &
    MAIN_PID=$!
    sleep 3
    
    # 2. Enterprise TMS (Port 3001)
    print_success "🏢 Starting Enterprise TMS on http://localhost:3001"
    cd enterprise-tms && npm run dev -- --port 3001 > ../logs/enterprise-tms.log 2>&1 &
    TMS_PID=$!
    cd ..
    sleep 2
    
    # 3. Advanced Frontend (Port 3002)
    print_success "🚀 Starting Advanced Frontend on http://localhost:3002"
    cd frontend && npm run dev -- --port 3002 > ../logs/advanced-frontend.log 2>&1 &
    FRONTEND_PID=$!
    cd ..
    sleep 2
}

# Start microservices with admin interfaces
start_microservices() {
    print_status "Starting microservices with admin interfaces..."
    
    # 4. API Gateway (Port 8080)
    print_success "🌐 Starting API Gateway on http://localhost:8080"
    cd microservices/services/api-gateway && npm start > ../../../logs/api-gateway.log 2>&1 &
    cd ../../..
    sleep 2
    
    # 5. Auth Service (Port 3003)
    print_success "🔐 Starting Auth Service on http://localhost:3003"
    cd microservices/services/auth-service && npm start > ../../../logs/auth-service.log 2>&1 &
    cd ../../..
    sleep 2
    
    # 6. ML Service with Admin Dashboard (Port 5000)
    print_success "🧠 Starting ML Service + Admin Dashboard on http://localhost:5000"
    if [ -f "microservices/services/ml-service/app/main.py" ]; then
        cd microservices/services/ml-service && python app/main.py > ../../../logs/ml-service.log 2>&1 &
        cd ../../..
        sleep 3
    else
        print_warning "⚠️  ML Service main.py not found, skipping..."
    fi

    # 7. Docling Processor with Admin (Port 5001)
    print_success "📄 Starting Docling Processor + Admin on http://localhost:5001"
    if [ -f "microservices/services/docling-processor/app/main.py" ]; then
        cd microservices/services/docling-processor && python app/main.py > ../../../logs/docling-processor.log 2>&1 &
        cd ../../..
        sleep 2
    else
        print_warning "⚠️  Docling Processor main.py not found, skipping..."
    fi
    
    # 8. QueenBee Anchor Service (Port 8000)
    print_success "👑 Starting QueenBee Anchor Service on http://localhost:8000"
    cd microservices/services/queenbee-anchor-service && python manage.py runserver 0.0.0.0:8000 > ../../../logs/queenbee-service.log 2>&1 &
    cd ../../..
    sleep 2
    
    # 9. Courier Service (Port 4000)
    print_success "🚚 Starting Courier Service on http://localhost:4000"
    if [ -f "microservices/services/courier-service/package.json" ]; then
        cd microservices/services/courier-service && npm install && npm start > ../../../logs/courier-service.log 2>&1 &
        cd ../../..
        sleep 2
    else
        print_warning "⚠️  Courier Service package.json not found, skipping..."
    fi
    
    # 10. Supplier Service with Admin (Port 6000)
    print_success "🏭 Starting Supplier Service + Admin on http://localhost:6000"
    if [ -f "microservices/services/supplier-service/package.json" ]; then
        cd microservices/services/supplier-service && npm install && npm start > ../../../logs/supplier-service.log 2>&1 &
        cd ../../..
        sleep 2
    else
        print_warning "⚠️  Supplier Service package.json not found, skipping..."
    fi

    # 11. Transport Service (Port 7000)
    print_success "🚛 Starting Transport Service on http://localhost:7000"
    if [ -f "microservices/services/transport-service/package.json" ]; then
        cd microservices/services/transport-service && npm install && npm start > ../../../logs/transport-service.log 2>&1 &
        cd ../../..
        sleep 2
    else
        print_warning "⚠️  Transport Service package.json not found, skipping..."
    fi
}

# Start specialized services
start_specialized_services() {
    print_status "Starting specialized services..."
    
    # 12. Drone Contractor Service (Port 8001)
    print_success "🚁 Starting Drone Contractor Service on http://localhost:8001"
    cd drone-contractor-service && docker-compose up -d > ../logs/drone-contractor.log 2>&1 &
    cd ..
    sleep 3
    
    # 13. eTenders Scraper Dashboard (Port 3009)
    print_success "🕷️ Starting eTenders Scraper Dashboard on http://localhost:3009"
    cd eTenders_Scrapper && python status_dashboard.py > ../logs/etenders-dashboard.log 2>&1 &
    cd ..
    sleep 2
    
    # 14. Enhanced Monitoring Dashboard (Port 3010)
    print_success "📊 Starting Enhanced Monitoring Dashboard on http://localhost:3010"
    cd eTenders_Scrapper && python enhanced_monitoring_dashboard.py > ../logs/enhanced-monitoring.log 2>&1 &
    cd ..
    sleep 2
}

# Create logs directory
mkdir -p logs

# Main execution
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                    🚀 BidBees Complete System Startup 🚀                    ║"
echo "║                                                                              ║"
echo "║  Starting ALL dashboards, admin interfaces, and monitoring tools            ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Cleanup existing processes
cleanup_ports

# Start all services
start_main_apps
start_microservices
start_specialized_services

# Wait for services to be ready
print_status "Waiting for all services to be ready..."
sleep 10

# Display comprehensive dashboard
echo -e "\n${GREEN}🎉 BidBees Complete System Started Successfully!${NC}\n"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                           🌟 MAIN DASHBOARDS 🌟                              ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "🏠 Main Client Dashboard:      ${GREEN}http://localhost:3000${NC}"
echo -e "📊 Bidding Dashboard:          ${GREEN}http://localhost:3000/bidding${NC}"
echo -e "📋 Tenders Browser:            ${GREEN}http://localhost:3000/tenders${NC}"
echo -e "🏢 Enterprise TMS:             ${GREEN}http://localhost:3001${NC}"
echo -e "🚀 Advanced Frontend:          ${GREEN}http://localhost:3002${NC}"

echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                          🛠️ ADMIN DASHBOARDS 🛠️                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "🧠 ML Service Admin:           ${GREEN}http://localhost:5000/admin${NC}"
echo -e "📄 Docling Admin:              ${GREEN}http://localhost:5001/admin${NC}"
echo -e "👑 QueenBee Admin:             ${GREEN}http://localhost:8000/admin${NC}"
echo -e "🏭 Supplier Admin:             ${GREEN}http://localhost:6000/admin${NC}"
echo -e "🚁 Drone Contractor Admin:    ${GREEN}http://localhost:8001/admin${NC}"

echo -e "\n${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${YELLOW}║                        📊 MONITORING & ANALYTICS 📊                         ║${NC}"
echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "🕷️ eTenders Scraper Status:    ${GREEN}http://localhost:3009${NC}"
echo -e "📈 Enhanced Monitoring:        ${GREEN}http://localhost:3010${NC}"
echo -e "🌐 API Gateway Health:         ${GREEN}http://localhost:8080/health${NC}"
echo -e "🔐 Auth Service Status:        ${GREEN}http://localhost:3003/health${NC}"

echo -e "\n${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                           🔧 MICROSERVICES 🔧                                ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "🚚 Courier Service:            ${GREEN}http://localhost:4000${NC}"
echo -e "🚛 Transport Service:          ${GREEN}http://localhost:7000${NC}"
echo -e "🌐 API Gateway:                ${GREEN}http://localhost:8080${NC}"
echo -e "🔐 Auth Service:               ${GREEN}http://localhost:3003${NC}"

echo -e "\n${RED}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${RED}║                            📚 API DOCUMENTATION 📚                           ║${NC}"
echo -e "${RED}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "📖 ML Service API Docs:        ${GREEN}http://localhost:5000/docs${NC}"
echo -e "📖 Docling API Docs:           ${GREEN}http://localhost:5001/docs${NC}"
echo -e "📖 API Gateway Swagger:        ${GREEN}http://localhost:8080/api-docs${NC}"

echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                              🎮 QUICK ACTIONS 🎮                             ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo -e "🛑 Stop All Services:          ${YELLOW}./stop-all-services.sh${NC}"
echo -e "📊 Check Service Status:       ${YELLOW}./check-status.sh${NC}"
echo -e "📝 View Logs:                  ${YELLOW}tail -f logs/*.log${NC}"

echo -e "\n${CYAN}🎉 All systems are now running! Explore the complete BidBees ecosystem! 🎉${NC}\n"

# Keep script running
echo -e "${YELLOW}Press Ctrl+C to stop all services...${NC}"
wait
